<template>
  <div class="flex flex-col h-full">
    <div class="flex px-9 py-4 bg-#F8F8F8 border-b-1 border-b-#D7D7D7 border-b-solid">
      <div class="flex flex-1 text-2xl text-#9E9E9E">{{ timeRange }}</div>
      <div class="flex">
        <ExportButton
          class="mr-2.5"
          :data="tableData"
          :columns="tableColumns"
          filename="全省母线负荷数据详情"
        />
        <n-button type="info" size="large" @click="handleBack">
          <template #icon>
            <n-icon><ChevronLeft20FilledIcon /></n-icon>
          </template>
          返回
        </n-button>
      </div>
    </div>

    <LineChart :data="chartData" height="450px" class="mt-2" />

    <div v-for="(item, index) in tableDataList" class="flex flex-col pl-4">
      <hr class="w-full shrink-0 border-t border-[#C0C0C0] border-dashed my-2.5" />

      <div class="flex">
        <LoadDetailCard class="w-50" :data="item" />
        <div class="flex flex-col px-5">
          <n-select
            :default-value="selectedSortOption"
            class="w-42.5 mb-2 self-end"
            :options="sortOptions"
            @update:value="handleSortOptionChange($event, index)"
          ></n-select>
          <!-- 横向表格数据 -->
          <VerticalTable
            class="flex-1"
            width="calc(100vw - 755px)"
            :columns="tableColumns"
            :data="item.busLfDataList"
            :loading="loading"
          ></VerticalTable>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { NButton, NSelect, NIcon, type SelectOption } from 'naive-ui'
import { useBusbarLoad } from '@/stores'

import LineChart from '@/components/shared/charts/LineChart.vue'
import ExportButton from '@/components/shared/buttons/ExportButton.vue'
import VerticalTable from '@/components/shared/tables/VerticalTable.vue'
import LoadDetailCard from './LoadDetailCard.vue'
import { ChevronLeft20FilledIcon } from '@/utils/constant/icons'

import { type BusbarLoadDataItem, type BusLfStatisticItem } from '@/utils/api/services/busbarLoad'
import type { SeriesData, TableColumn } from '@/types'
import { formatTime } from '@/utils/tools'

const emit = defineEmits(['on-back'])

const props = withDefaults(
  defineProps<{
    tableColumns?: TableColumn[]
    tableDataList?: BusLfStatisticItem[]
    chartData?: SeriesData[]
    selectedSortValue?: string
    sortOptions?: SelectOption[]
    loading?: boolean
  }>(),
  {
    tableColumns: () => [],
    tableDataList: () => [],
    chartData: () => [],
    selectedSortValue: 'dailyAvgUsDiff',
    sortOptions: () => [],
    loading: false,
  },
)

const selectedSortOption = ref(props.selectedSortValue)
const busbarLoadStore = useBusbarLoad()
const tableData = ref<BusbarLoadDataItem[]>([])

const timeRange = computed(() => {
  if (!busbarLoadStore.timeRange.length) return ''
  return `${formatTime(busbarLoadStore.timeRange[0])} ~ ${formatTime(busbarLoadStore.timeRange[1])}`
})

const handleSortOptionChange = (value: keyof BusbarLoadDataItem, index: number) => {
  props.tableDataList[index].busLfDataList.sort((a, b) => {
    return parseFloat(b[value]) - parseFloat(a[value])
  })
}

const handleBack = () => {
  emit('on-back')
}
</script>

<style></style>
