import { API_ENDPOINTS } from '../config'
import { api } from '../instance'

// 日报查询参数接口
export interface DailyReportQueryParams {
  day: string
}

// 调峰时段
export interface PeakingPeriodItem {
  startTime: string
  endTime: string
  type: string
  maxValue: string
}

//调峰详情
export interface PeakingDetailItem {
  stationName: string
  stationId: string
  value: string
}

//调峰时段详情
export interface PeakingTimeDetailItem {
  time: string
  type: string
  peakingDetailList: PeakingDetailItem[]
}

// 新能源数据 曲线列表
export interface NewEnergyDataItem {
  time: string
  totalPower: string
  totalPrediction: string
  windPower: string
  windPrediction: string
  pvPower: string
  pvPrediction: string
}

//  日报响应数据接口
export interface DailyReportResponse {
  description: string
  peakingPeriodList: PeakingPeriodItem[]
  peakingTimeDetailList: PeakingTimeDetailItem[]
  newEnergyDataList: NewEnergyDataItem[]
}

//  日报服务类
export class DailyReportService {
  /**
   *  获取日报数据
   * @param params 查询参数
   * @returns  日报数据
   */
  static async getDailyReportData(params: DailyReportQueryParams): Promise<DailyReportResponse> {
    return api.get<DailyReportResponse>(API_ENDPOINTS.DAILY_REPORT.DAILY_REPORT_DATA, params)
  }
}
