<template>
  <div class="flex flex-col h-full">
    <!-- 顶部搜索表单 -->
    <SearchForm :loading="loading" @search="handleSearch">
      <template #form-fields>
        <InputGroup size="large" label="时间" class="mr-2.5 max-w-500px">
          <n-date-picker
            v-model:value="busbarLoadStore.timeRange"
            class="w-full"
            type="daterange"
            size="large"
            placeholder="请选择时间范围"
            :is-date-disabled="disablePreviousDate"
          >
          </n-date-picker>
        </InputGroup>
        <!-- <InputGroup size="large" label="名称" class="mr-2.5 max-w-317px">
          <n-select
            v-model:value="searchValue"
            :options="cityOptions"
            size="large"
            placeholder="请选择名称"
            clearable
          >
          </n-select>
        </InputGroup> -->
      </template>

      <template #action-buttons>
        <ExportButton
          :data="tableData"
          :columns="tableColumns.slice(0, 4)"
          filename="全省母线负荷数据"
        />
      </template>
    </SearchForm>
    <LineChart ref="chartRef" :data="chartData" yAxisName="万千瓦" height="450px" class="mt-2" />

    <!-- 底部数据展示区域 -->
    <DataTable
      class="px-5"
      :columns="tableColumns"
      :data="tableData"
      :loading="loading"
      height="calc(100vh - 595px)"
      @sort="handleSort"
    >
      <template #action="{ item, index }">
        <n-button type="primary" @click="handleViewInfo(item)"> 查看 </n-button>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, toRaw, useTemplateRef, watch } from 'vue'
import { NDatePicker, NButton, NSelect } from 'naive-ui'

import {
  disablePreviousDate,
  formatTime,
  getTodayTimeRange,
  isTimeRangeExceed,
} from '@/utils/tools/'
import { useBusbarLoad } from '@/stores'
import { applySorting } from '@/utils/sort'

import LineChart from '@/components/shared/charts/LineChart.vue'
import DataTable from '@/components/shared/tables/DataTable.vue'
import InputGroup from '@/components/shared/InputGroup.vue'
import SearchForm from '@/components/shared/SearchForm.vue'
import ExportButton from '@/components/shared/buttons/ExportButton.vue'
import { BusbarLoadService, type BusbarLoadDataItem } from '@/utils/api/services/busbarLoad'
import type { SeriesData } from '@/types'

const cityOptions = [
  { label: '南京', value: '320100' },
  { label: '无锡', value: '320200' },
  { label: '徐州', value: '320300' },
  { label: '常州', value: '320400' },
  { label: '苏州', value: '320500' },
  { label: '南通', value: '320600' },
  { label: '连云港', value: '320700' },
  { label: '淮安', value: '320800' },
  { label: '盐城', value: '320900' },
  { label: '扬州', value: '321000' },
  { label: '镇江', value: '321100' },
  { label: '泰州', value: '321200' },
  { label: '宿迁', value: '321300' },
]
// 默认表格列配置
const tableColumns = [
  {
    key: 'time',
    title: '时间',
    sorter: {},
  },
  {
    key: 'rtValue',
    title: '实时负荷',
    sorter: {},
  },
  {
    key: 'usPrediction',
    title: '负荷预测值',
    sorter: {},
  },
  {
    key: 'usDiff',
    title: '负荷偏差',
    sorter: {},
  },
  {
    key: 'action',
    title: '详情',
  },
]

const busbarLoadStore = useBusbarLoad()
const loading = ref(false)
const chartRef = useTemplateRef('chartRef')
const rowData = ref<BusbarLoadDataItem[]>([])
const tableData = ref<BusbarLoadDataItem[]>([])
const searchValue = ref('')

const chartData = computed<SeriesData[]>(() => {
  if (!rowData.value.length) return []

  return [
    {
      name: '负荷预测',
      color: 'rgba(206, 66, 174, 1)',
      isShowAreaStyle: true,
      data: rowData.value.map((item) => ({
        name: item.time,
        value: item.usPrediction,
      })),
    },
    {
      name: '负荷实测',
      color: 'rgba(12, 163, 159, 1)',
      isShowAreaStyle: true,
      data: rowData.value.map((item) => ({
        name: item.time,
        value: item.rtValue,
      })),
    },
    {
      name: '负荷偏差',
      color: '#004EE4',
      yAxisIndex: 2,
      data: rowData.value.map((item) => ({
        name: item.time,
        value: item.usDiff,
      })),
    },
  ]
})

const fetchBusbarLoadData = async () => {
  loading.value = true
  try {
    rowData.value = await BusbarLoadService.getBusbarLoadData({
      day: formatTime(getTodayTimeRange()[0], 'YYYY-MM-DD'),
    })
    tableData.value = JSON.parse(JSON.stringify(toRaw(rowData.value)))
    // 处理响应数据
  } catch (error) {
    console.error('获取全省母线负荷数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索处理函数
const handleSearch = () => {
  // 判断时间范围是否超过31天
  if (isTimeRangeExceed(busbarLoadStore.timeRange)) {
    window.$message.warning('时间范围不能超过31天')
    return
  }

  fetchBusbarLoadData()
  // 跳转到ProvinceDetail页面
  busbarLoadStore.isDetailVisible = true
  busbarLoadStore.isInfoVisible = false
}

const handleViewInfo = (item: BusbarLoadDataItem) => {
  busbarLoadStore.selectedRow = item
  // 跳转到ProvinceInfo页面
  busbarLoadStore.isInfoVisible = true
  busbarLoadStore.isDetailVisible = false
}

const handleSort = (column: any, order: 'asc' | 'desc' | null, multiSortState: any) => {
  if (!order && Object.keys(multiSortState).length === 0) {
    // 恢复默认排序
    tableData.value = JSON.parse(JSON.stringify(toRaw(rowData.value)))
    return
  }

  // 使用公共的多列排序函数
  tableData.value = applySorting(tableData.value, multiSortState, tableColumns)
}

watch(
  () => [busbarLoadStore.isDetailVisible, busbarLoadStore.isInfoVisible],
  ([isDetailVisible, isInfoVisible]) => {
    //  都为false的时候，刷新lineChart大小
    if (!isDetailVisible && !isInfoVisible) {
      chartRef.value?.resize()
    }
  },
)

onMounted(() => {
  fetchBusbarLoadData()
})
</script>

<style></style>
