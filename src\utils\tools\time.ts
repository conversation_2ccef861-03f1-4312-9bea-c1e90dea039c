import dayjs, { Dayjs } from 'dayjs'

/**
 *  格式化时间
 * @param time
 * @param format
 * @returns
 */
export const formatTime = (time: number | string, format = 'YYYY-MM-DD HH:mm:ss') => {
  return dayjs(time).format(format)
}

// 获取当日0点到23:59:59的时间戳
export const getTodayTimeRange = () => {
  const start = dayjs().startOf('day').valueOf()
  const end = dayjs().endOf('day').valueOf()
  return [start, end]
}

// 判断时间范围是否跨天
export const isTimeRangeCrossDay = (timeRange: [number, number] | null): boolean => {
  if (!timeRange || timeRange.length !== 2) {
    return false
  }

  const [startTime, endTime] = timeRange
  const startDay = dayjs(startTime).format('YYYY-MM-DD')
  const endDay = dayjs(endTime).format('YYYY-MM-DD')

  return startDay !== endDay
}

//判断时间段是否超过一定的天数，默认31天
export const isTimeRangeExceed = (timeRange: [number, number] | null, days = 31): boolean => {
  if (!timeRange || timeRange.length !== 2) {
    return false
  }

  const [startTime, endTime] = timeRange
  const diffDays = dayjs(endTime).diff(dayjs(startTime), 'day')

  return diffDays > days
}

/**
 *  禁用选择器中之前的时间
 * @param ts
 * @returns
 */
export const disablePreviousDate = (ts: number) => {
  return ts > getTodayTimeRange()[1]
}

const TIMER_INTERVAL = 1000 * 60

/**
 * 传入函数，每隔一段时间自动执行
 * 页面卸载时会自动清除定时器
 */
export const autoExecute = (fn: Function, interval = TIMER_INTERVAL) => {
  fn()
  const timer = setInterval(fn, interval)
  return () => {
    clearInterval(timer)
  }
}

/**
 * 判断是否是完整日期（YYYY-MM-DD）或日期时间格式
 */
export const isFullDate = (val: string): boolean => {
  return /^\d{4}-\d{2}-\d{2}( \d{2}:\d{2}(:\d{2})?)?$/.test(val)
}

/**
 * 判断是否是纯时间（HH:mm 或 HH:mm:ss）
 */
export const isTimeOnly = (val: string): boolean => {
  return /^([01]\d|2[0-3]):[0-5]\d(:[0-5]\d)?$/.test(val)
}

/** 重载定义 */
export function parseTime(val: string, isFormat: true): string
export function parseTime(val: string, isFormat?: false): Dayjs

export function parseTime(val: string, isFormat = false): string | Dayjs {
  const today = dayjs().format('YYYY-MM-DD')
  const date = dayjs(`${today} ${val}`)
  return isFormat ? date.format('YYYY-MM-DD HH:mm:ss') : date
}
