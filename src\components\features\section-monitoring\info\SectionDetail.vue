<template>
  <div class="flex bg-#FEF4E8 rounded mx-7.5">
    <div
      v-for="(item, index) in sectionDetail"
      :key="index"
      class="flex flex-auto flex-col flex-grow-1 px-5 py-4"
    >
      <span class="text-#CC8F60 text-base font-normal flex items-center"
        ><span class="text-5xl mr-1">·</span> {{ item.label }}</span
      >
      <span class="text-#643F1A text-xl font-medium ml-3.5">{{ item.value }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { SectionStatisticDetailResponse } from '@/utils/api'

interface Props {
  sectionData: SectionStatisticDetailResponse | null
}

const props = defineProps<Props>()

const sectionDetail = computed(() => {
  if (!props.sectionData) {
    return {
      name: { label: '断面名称', value: '-' },
      volt: { label: '电压等级', value: '-' },
      time: { label: '统计时段', value: '-' },
      maxFlow: { label: '最大越限潮流', value: '-' },
      maxValue: { label: '越限最大值', value: '-' },
      totalOverLimitTime: { label: '总越限时间', value: '-' },
      longestOverLimitTime: { label: '最长出线越限时长', value: '-' },
    }
  }

  return {
    name: {
      label: '断面名称',
      value: props.sectionData.sectionName,
    },
    volt: {
      label: '电压等级',
      value: props.sectionData.volt,
    },
    time: {
      label: '统计时段',
      value: props.sectionData.period,
    },
    maxFlow: {
      label: '最大越限潮流',
      value: props.sectionData.maxValue,
    },
    maxValue: {
      label: '越限最大值',
      value: props.sectionData.maxDiffValue,
    },
    totalOverLimitTime: {
      label: '总越限时间',
      value: props.sectionData.totalOverTime,
    },
    longestOverLimitTime: {
      label: '最长出线越限时长',
      value: props.sectionData.longestOverTime,
    },
  }
})
</script>
