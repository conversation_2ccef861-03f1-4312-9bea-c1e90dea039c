import type { ChartImageConfig, WordContentItem } from '@/types'
import { exportToWord } from '@/utils/export'
import { AlignmentType, HeadingLevel, LineRuleType } from 'docx'

export const useExportDaliyReport = () => {
  const exportWord = ({
    fileName,
    statisticChartImage,
    windChartImage,
    solarChartImage,
    chartTime,
    peakShavingData,
    description,
    peakingTimeDetailTableData,
  }: {
    fileName: string
    statisticChartImage?: ChartImageConfig
    windChartImage?: ChartImageConfig
    solarChartImage?: ChartImageConfig
    chartTime: string
    peakShavingData: string[][]
    description: string
    peakingTimeDetailTableData: { time: string; data: string[][] }[]
  }) => {
    const content: WordContentItem[] = [
      {
        type: 'text',
        data: '新能源市场出清调峰日报',
        style: {
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 200,
          },
        },
        textStyle: {
          font: '方正小标宋_GBK',
          size: 44,
          bold: false,
          color: '#000000',
        },
      },
      // {
      //   type: 'text',
      //   data: '',
      //   textStyle: {
      //     font: '方正小标宋_GBK',
      //     size: 44,
      //     bold: true,
      //     color: '#000000',
      //   },
      // },
      {
        type: 'text',
        data: '一、新能源市场出清调峰情况',
        style: {
          heading: HeadingLevel.HEADING_1,
          alignment: AlignmentType.LEFT,
          spacing: {
            line: 560,
            lineRule: LineRuleType.EXACT,
          },
        },
        textStyle: {
          font: '方正黑体_GBK',
          size: 32,
          bold: false,
          color: '#000000',
        },
      },
      {
        type: 'text',
        data: description,
        style: {
          alignment: AlignmentType.LEFT,
          spacing: {
            line: 560, // 28磅的行高
            lineRule: LineRuleType.EXACT,
          },
          indentFirstLine: true,
        },
        textStyle: {
          font: '方正仿宋_GBK',
          size: 32,
        },
      },
      {
        type: 'table',
        data: peakShavingData,
        style: {
          width: 100,
          borderColor: '#000000',
          fontSize: 24,
        },
      },
      {
        type: 'text',
        data: '二、新能源弃电情况统计',
        style: {
          heading: HeadingLevel.HEADING_1,
          alignment: AlignmentType.LEFT,
          spacing: {
            line: 560,
            lineRule: LineRuleType.EXACT,
          },
        },
        textStyle: {
          font: '方正黑体_GBK',
          size: 32,
          bold: false,
          color: '#000000',
        },
      },
      {
        type: 'image',
        data: statisticChartImage?.base64,
        style: {
          width: statisticChartImage?.width,
          height: statisticChartImage?.height,
        },
      },
      {
        type: 'text',
        data: `图1  ${chartTime}实时市场新能源弃电情况统计`,
        style: {
          alignment: AlignmentType.CENTER,
          spacing: {
            line: 560,
            lineRule: LineRuleType.EXACT,
          },
        },
        textStyle: {
          font: '方正仿宋_GBK',
          size: 32,
          bold: false,
          color: '#000000',
        },
      },
      {
        type: 'image',
        data: windChartImage?.base64,
        style: {
          width: windChartImage?.width,
          height: windChartImage?.height,
        },
      },
      {
        type: 'text',
        data: `图2  ${chartTime}实时市场风电弃电情况统计`,
        style: {
          alignment: AlignmentType.CENTER,
          spacing: {
            line: 560,
            lineRule: LineRuleType.EXACT,
          },
        },
        textStyle: {
          font: '方正仿宋_GBK',
          size: 32,
          bold: false,
          color: '#000000',
        },
      },
      {
        type: 'image',
        data: solarChartImage?.base64,
        style: {
          width: solarChartImage?.width,
          height: solarChartImage?.height,
        },
      },
      {
        type: 'text',
        data: `图3  ${chartTime}实时市场光伏弃电情况统计`,
        style: {
          alignment: AlignmentType.CENTER,
          spacing: {
            line: 560,
            lineRule: LineRuleType.EXACT,
          },
        },
        textStyle: {
          font: '方正仿宋_GBK',
          size: 32,
          bold: false,
          color: '#000000',
        },
      },
      {
        type: 'text',
        data: '三、风电、光伏厂站调峰明细',
        style: {
          heading: HeadingLevel.HEADING_1,
          alignment: AlignmentType.LEFT,
          spacing: {
            line: 560,
            lineRule: LineRuleType.EXACT,
          },
        },
        textStyle: {
          font: '方正黑体_GBK',
          size: 32,
          bold: false,
          color: '#000000',
        },
      },
    ]

    peakingTimeDetailTableData.forEach((item) => {
      content.push({
        type: 'text',
        data: `${item.time} 调峰时段`,
        style: {
          alignment: AlignmentType.LEFT,
          spacing: {
            line: 560,
            lineRule: LineRuleType.EXACT,
          },
        },
        textStyle: {
          font: '方正仿宋_GBK',
          size: 32,
          bold: false,
          color: '#000000',
        },
      })
      content.push({
        type: 'text',
        data: '单位：万千瓦',
        style: {
          alignment: AlignmentType.RIGHT,
          spacing: {
            line: 560,
            lineRule: LineRuleType.EXACT,
          },
        },
        textStyle: {
          font: '方正仿宋_GBK',
          size: 32,
          bold: false,
          color: '#000000',
        },
      })
      content.push({
        type: 'table',
        data: item.data,
        style: {
          width: 100,
          borderColor: '#000000',
          fontSize: 24,
        },
      })
    })

    exportToWord(fileName + '.docx', content)
  }

  return {
    exportWord,
  }
}
