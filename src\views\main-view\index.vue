<template>
  <n-flex>
    <n-layout position="absolute" has-sider>
      <Siderbar></Siderbar>
      <n-layout :native-scrollbar="false">
        <RouterView />
      </n-layout>
    </n-layout>
  </n-flex>
</template>

<script setup lang="ts">
import { NLayout, NFlex } from 'naive-ui'
import { RouterView } from 'vue-router'
import Siderbar from '@/components/layouts/Siderbar.vue'

import { useMessage } from 'naive-ui'

window.$message = useMessage()
</script>
