<template>
  <KeepAlive :include="['CpsStatistic']">
    <component :is="cpsFrequencyStore.isInfoVisible ? CpsInfo : CpsStatistic"></component>
  </KeepAlive>
</template>

<script setup lang="ts">
import CpsStatistic from '@/components/features/cps-frequency/statistic/CpsStatistic.vue'
import CpsInfo from '@/components/features/cps-frequency/info/CpsInfo.vue'

import { useCpsFrequency } from '@/stores'

const cpsFrequencyStore = useCpsFrequency()
</script>
<style scoped></style>
