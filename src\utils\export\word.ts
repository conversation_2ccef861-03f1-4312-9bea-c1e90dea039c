// Word 导出工具 —— 按需加载示例
import type { WordContentItem } from '@/types'
import { saveAs } from './index'

// 按需加载 docx，并缓存引用
let docxModules: Promise<any> | null = null
function loadDocx() {
  if (!docxModules) {
    docxModules = import('docx')
  }
  return docxModules
}

/**
 * 导出 Word 文档（异步按需加载）
 */
export const exportToWord = async (filename = '导出文档.docx', data: WordContentItem[]) => {
  if (!data?.length) throw new Error('没有数据可导出')

  // 动态 import docx
  const {
    Document,
    Packer,
    Paragraph,
    TextRun,
    Table,
    TableCell,
    TableRow,
    ImageRun,
    AlignmentType,
    WidthType,
    BorderStyle,
    convertInchesToTwip,
  } = await loadDocx()

  // 文本段落
  const generateText = (item: WordContentItem & { type: 'text' }) => {
    const { data: text, style = {}, textStyle = {} } = item
    const indentFirstLine = style.indentFirstLine ? convertInchesToTwip(0.5) : undefined
    const numbering = style.numbering
      ? { reference: style.numbering.reference, level: style.numbering.level }
      : undefined

    return new Paragraph({
      heading: style.heading,
      alignment: style.alignment || AlignmentType.LEFT,
      spacing: style.spacing,
      numbering,
      indent: { firstLine: indentFirstLine },
      children: [
        new TextRun({
          text,
          font: textStyle.font || '仿宋_GB2312',
          size: textStyle.size || 32,
          bold: textStyle.bold || false,
          color: textStyle.color || '#000000',
        }),
      ],
    })
  }

  // 表格
  const generateTable = (item: WordContentItem & { type: 'table' }) => {
    const { data: rowsData, style = {} } = item
    if (!rowsData?.length) return new Paragraph('')

    const totalWidth = convertInchesToTwip(6)
    const colCount = rowsData[0].length
    const cellW = Math.floor(totalWidth / colCount)

    const rows = rowsData.map(
      (row, i) =>
        new TableRow({
          children: row.map(
            (cell) =>
              new TableCell({
                width: { size: cellW, type: WidthType.DXA },
                children: [
                  new Paragraph({
                    children: [
                      new TextRun({ text: cell, size: style.fontSize || 24, bold: i === 0 }),
                    ],
                    alignment: AlignmentType.CENTER,
                  }),
                ],
                margins: {
                  top: convertInchesToTwip(0.05),
                  bottom: convertInchesToTwip(0.05),
                  left: convertInchesToTwip(0.1),
                  right: convertInchesToTwip(0.1),
                },
              }),
          ),
        }),
    )

    return new Table({
      width: { size: totalWidth, type: WidthType.DXA },
      columnWidths: Array(colCount).fill(cellW),
      rows,
      borders: {
        top: { style: BorderStyle.SINGLE, size: 1, color: style.borderColor || '#000000' },
        bottom: { style: BorderStyle.SINGLE, size: 1, color: style.borderColor || '#000000' },
        left: { style: BorderStyle.SINGLE, size: 1, color: style.borderColor || '#000000' },
        right: { style: BorderStyle.SINGLE, size: 1, color: style.borderColor || '#000000' },
        insideHorizontal: {
          style: BorderStyle.SINGLE,
          size: 1,
          color: style.borderColor || '#000000',
        },
        insideVertical: {
          style: BorderStyle.SINGLE,
          size: 1,
          color: style.borderColor || '#000000',
        },
      },
    })
  }

  // 图片
  const base64ToUint8Array = (b64: string): Uint8Array => {
    const bin = atob(b64)
    const arr = new Uint8Array(bin.length)
    for (let i = 0; i < bin.length; i++) arr[i] = bin.charCodeAt(i)
    return arr
  }

  const generateImage = (item: WordContentItem & { type: 'image' }) => {
    if (!item.data) return new Paragraph('')
    let { width = 600, height = 300 } = item.style || {}
    const maxW = 600
    if (width > maxW) {
      height = (height / width) * maxW
      width = maxW
    }

    const base64 = item.data.replace(/^data:image\/[a-z]+;base64,/, '')
    const type = item.data.match(/^data:image\/([a-z]+);base64,/)?.[1] || 'png'
    const buffer = base64ToUint8Array(base64)

    return new Paragraph({
      children: [new ImageRun({ data: buffer, transformation: { width, height }, type })],
      alignment: AlignmentType.CENTER,
      spacing: { before: 200, after: 200 },
    })
  }

  // 组装
  const children = data.map((item) => {
    switch (item.type) {
      case 'text':
        return generateText(item)
      case 'table':
        return generateTable(item)
      case 'image':
        return generateImage(item)
      default:
        return new Paragraph('')
    }
  })

  const doc = new Document({ sections: [{ children }] })
  const blob = await Packer.toBlob(doc)
  saveAs(blob, filename)
}
