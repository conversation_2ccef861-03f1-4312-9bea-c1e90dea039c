{"name": "spot-market-operation-quality-analysis", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.10.0", "dayjs": "^1.11.13", "docx": "^9.5.1", "echarts": "^5.6.0", "exceljs": "^4.4.0", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.16.4", "@unocss/eslint-config": "^66.3.3", "@vitejs/plugin-legacy": "^7.0.1", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.31.0", "eslint-plugin-oxlint": "~1.7.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "naive-ui": "^2.42.0", "npm-run-all2": "^8.0.4", "oxlint": "~1.7.0", "prettier": "3.6.2", "rollup-plugin-visualizer": "^6.0.3", "typescript": "~5.8.3", "unocss": "^66.3.3", "vite": "^7.0.5", "vite-plugin-vue-devtools": "^7.7.7", "vite-svg-loader": "^5.1.0", "vue-tsc": "^3.0.2"}}