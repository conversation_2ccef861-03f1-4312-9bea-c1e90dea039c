import { createRouter, createWebHashHistory } from 'vue-router'
import HomeView from '../views/main-view/index.vue'

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      redirect: '/section-monitoring',
      children: [
        {
          path: 'section-monitoring',
          name: 'section-monitoring',
          component: () => import('../views/section-monitoring/index.vue'),
        },
        {
          path: 'daily-report',
          name: 'daily-report',
          component: () => import('../views/daily-report/index.vue'),
        },
        {
          path: 'load-forecasting/unified-scheduling-load',
          name: 'unified-scheduling-load',
          component: () => import('../views/load-forecasting/unified-scheduling-load/index.vue'),
        },
        {
          path: 'load-forecasting/busbar-load',
          name: 'busbar-load',
          component: () => import('../views/load-forecasting/busbar-load/index.vue'),
        },
        {
          path: 'cps-frequency',
          name: 'cps-frequency',
          component: () => import('../views/cps-frequency/index.vue'),
        },
      ],
    },
  ],
})

export default router
