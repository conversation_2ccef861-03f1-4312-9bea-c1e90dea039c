<template>
  <!-- 日期详情数据 -->
  <div class="flex flex-shrink-0 flex-col w-40.5 bg-#FEF4E8 rounded">
    <div class="text-#C76920 text-xl mt-3 mb-2 ml-4">
      <div class="flex items-center mb-2">
        <n-icon size="18">
          <CalendarAltRegularIcon />
        </n-icon>
        <span class="ml-2"> {{ data.date }}</span>
      </div>
      <div class="flex items-center">
        <n-icon size="18">
          <CalendarAltRegularIcon />
        </n-icon>
        <span class="ml-2"> {{ data.name }}</span>
      </div>
    </div>
    <div class="flex flex-1 flex-col pl-2 pb-4 justify-between">
      <div class="flex items-center -mt-2">
        <div class="flex items-center text-#CC8F60">
          <span class="mb-1 text-5xl mr-0.5">·</span>
          <span class="text-base">日均偏差</span>
        </div>
        <div class="text-#643F1A text-xl ml-2">{{ data.dailyAvgUsDiff }}</div>
      </div>
      <div class="flex items-center -mt-5">
        <div class="flex items-center text-#CC8F60">
          <span class="mb-1 text-5xl mr-0.5">·</span>
          <span class="text-base">日均偏差率</span>
        </div>
        <div class="text-#643F1A text-xl ml-2">{{ data.dailyAvgUsDiffRate }}</div>
      </div>
      <div class="flex items-center -mt-5">
        <div class="flex items-center text-#CC8F60">
          <span class="text-5xl mr-0.5">·</span>
          <span class="mt-1 text-base">MAE</span>
        </div>
        <div class="text-#643F1A text-xl ml-5">{{ data.mae }}</div>
      </div>
      <div class="flex items-center -mt-5">
        <div class="flex items-center text-#CC8F60">
          <span class="text-5xl mr-0.5">·</span>
          <span class="mt-1 text-base">RMSE</span>
        </div>
        <div class="text-#643F1A text-xl ml-5">{{ data.rmse }}</div>
      </div>
      <div class="flex items-center -mt-5">
        <div class="flex items-center text-#CC8F60">
          <span class="text-5xl mr-0.5">·</span>
          <span class="mt-1 text-base">MAPE</span>
        </div>
        <div class="text-#643F1A text-xl ml-5">{{ data.mape }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NIcon } from 'naive-ui'
import type { BusLfStatisticItem } from '@/utils/api/services/busbarLoad'
import { CalendarAltRegularIcon } from '@/utils/constant/icons'

withDefaults(
  defineProps<{
    data: BusLfStatisticItem
  }>(),
  {
    data: () => ({
      date: '',
      name: '',
      dailyAvgUsDiff: '',
      dailyAvgUsDiffRate: '',
      mae: '',
      rmse: '',
      mape: '',
      busLfDataList: [],
    }),
  },
)
</script>

<style></style>
