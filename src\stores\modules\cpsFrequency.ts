import type { CPSStatisticResponse } from '@/utils/api'
import { defineStore } from 'pinia'
import { ref } from 'vue'

import LineChart from '@/components/shared/charts/LineChart.vue'
import { getTodayTimeRange } from '@/utils/tools'

export const useCpsFrequency = defineStore('cpsFrequency', () => {
  const isInfoVisible = ref<boolean>(false)
  const cpsData = ref<CPSStatisticResponse | null>(null)

  const timestampRange = getTodayTimeRange()

  const timeRange = ref<[number, number]>([timestampRange[0], timestampRange[1]])

  const chartRef = ref<InstanceType<typeof LineChart> | null>(null)

  return {
    isInfoVisible,
    cpsData,
    timeRange,
    chartRef,
  }
})
