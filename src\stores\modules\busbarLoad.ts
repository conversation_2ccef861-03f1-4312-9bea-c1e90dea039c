import { defineStore } from 'pinia'
import { ref } from 'vue'

import LineChart from '@/components/shared/charts/LineChart.vue'
import { getTodayTimeRange } from '@/utils/tools'
import type { BusbarLoadDataItem } from '@/utils/api/services/busbarLoad'

export const useBusbarLoad = defineStore('busbarLoad', () => {
  const isInfoVisible = ref<boolean>(false)
  const isDetailVisible = ref<boolean>(false)

  const timestampRange = getTodayTimeRange()

  const timeRange = ref<[number, number]>([timestampRange[0], timestampRange[1]])

  const chartRef = ref<InstanceType<typeof LineChart> | null>(null)

  const selectedRow = ref<BusbarLoadDataItem | null>(null)

  return {
    isInfoVisible,
    isDetailVisible,
    timeRange,
    chartRef,
    selectedRow,
  }
})
