<template>
  <div>
    <div ref="chartRef" :style="{ width: width, height: height }"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { init, graphic, type ECharts } from 'echarts/core'
import type { BarChartProps, GradientColors } from '@/types/chart'
import { DEFAULT_CHART_THEME } from '@/types/chart'

const props = withDefaults(defineProps<BarChartProps>(), {
  title: '',
  width: '100%',
  height: '400px',
  showGrid: true,
  showLegend: true,
})

const chartRef = ref<HTMLDivElement>()
let chartInstance: ECharts | null = null

// 深度合并对象
const deepMerge = (target: any, source: any): any => {
  if (!source) return target
  if (Array.isArray(target) && Array.isArray(source)) {
    return target.concat(source)
  }

  const result = { ...target }

  for (const key in source) {
    if (source[key] !== null && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = deepMerge(result[key] || {}, source[key])
    } else {
      result[key] = source[key]
    }
  }

  return result
}

// 创建渐变色
const createGradientColor = (colors: GradientColors) => {
  return new graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 0,
      color: colors.start,
    },
    {
      offset: 1,
      color: colors.end,
    },
  ])
}

// 获取图表配置
const getChartOption = () => {
  const series = props.data.map((item, index) => {
    // 默认系列配置
    const defaultSeriesConfig = {
      name: item.name,
      yAxisIndex: item.yAxisIndex || 0,
      type: 'bar',
      data: item.data.map((point) => point.value),
      label: {
        show: true, // 开启标签
        position: 'top', // 放在柱子顶部
        formatter: '{c}', // {c} 会被替换成对应数据值
        fontSize: 12,
        color: item.color,
      },
      itemStyle: {
        color: item.color,
        borderRadius: [4, 4, 0, 0], // 顶部圆角
      },
    }

    // 合并自定义系列配置
    return props.customConfig?.series
      ? deepMerge(defaultSeriesConfig, props.customConfig.series)
      : defaultSeriesConfig
  })

  // 获取 X 轴数据
  const xAxisData = props.xAxisData || props.data[0]?.data.map((point) => point.name) || []

  // 是否显示频率轴
  const isShowFrequencyAxis = props.data.some((item) => item.yAxisIndex === 1)

  // 是否显示偏差轴
  const isShowDiffAxis = props.data.some((item) => item.yAxisIndex === 2)

  // 默认配置
  const defaultOption = {
    title: {
      text: props.title,
      left: 20,
      textStyle: {
        color: DEFAULT_CHART_THEME.textColor,
        fontSize: 20,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: DEFAULT_CHART_THEME.gridColor,
      borderWidth: 1,
      textStyle: {
        color: DEFAULT_CHART_THEME.textColor,
      },
      axisPointer: {
        type: 'shadow', // 柱状图使用阴影指示器
      },
    },
    legend: {
      show: props.showLegend,
      icon: 'rect', // 正方形图例
      itemWidth: 14,
      itemHeight: 14,
      top: isShowFrequencyAxis || isShowDiffAxis ? 0 : 5,
      right: 20,
      textStyle: {
        color: DEFAULT_CHART_THEME.textColor,
        fontSize: 20,
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: props.showLegend ? '17%' : '5%',
      containLabel: true,
      borderColor: DEFAULT_CHART_THEME.gridColor,
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: DEFAULT_CHART_THEME.axisLineColor,
        },
      },
      axisTick: {
        show: false,
        alignWithLabel: true,
      },
      axisLabel: {
        color: '#909399',
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '兆瓦',
        nameTextStyle: {
          color: DEFAULT_CHART_THEME.textColor,
          fontSize: 20,
        },
        axisLine: {},
        axisTick: {},
        axisLabel: {
          color: '#909399',
        },
        splitLine: {
          lineStyle: {
            color: DEFAULT_CHART_THEME.splitLineColor,
          },
        },
      },
      {
        type: 'value',
        name: '赫兹',
        show: isShowFrequencyAxis,
        nameTextStyle: {
          color: DEFAULT_CHART_THEME.textColor,
          fontSize: 20,
        },
        splitLine: {
          lineStyle: {
            color: DEFAULT_CHART_THEME.splitLineColor,
          },
        },
      },
      {
        type: 'value',
        name: '兆瓦',
        show: isShowDiffAxis,
        nameTextStyle: {
          color: DEFAULT_CHART_THEME.textColor,
          fontSize: 20,
        },
        splitLine: {
          lineStyle: {
            color: DEFAULT_CHART_THEME.splitLineColor,
          },
        },
      },
    ],
    series,
  }

  // 如果没有自定义配置，直接返回默认配置
  if (!props.customConfig) {
    return defaultOption
  }

  // 合并自定义配置
  const mergedOption = deepMerge(defaultOption, {
    title: props.customConfig.title || {},
    legend: props.customConfig.legend || {},
    grid: props.customConfig.grid || {},
  })

  return mergedOption
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = init(chartRef.value)

  chartInstance.setOption(getChartOption())
}

// 更新图表
const updateChart = () => {
  if (chartInstance) {
    chartInstance.setOption(getChartOption(), true)
  }
}

const getImg = () => {
  if (chartInstance) {
    const width = chartInstance.getWidth()
    const height = chartInstance.getHeight()
    return {
      width,
      height,
      base64: chartInstance.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff',
      }),
    }
  }
  return {
    width: 0,
    height: 0,
    base64: '',
  }
}

// 监听数据变化
watch(
  () => props.data,
  () => {
    updateChart()
  },
  { deep: true },
)

// 监听其他配置变化
watch(
  [() => props.showGrid, () => props.showLegend, () => props.customConfig],
  () => {
    updateChart()
  },
  { deep: true },
)

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(async () => {
  await nextTick()
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})

// 暴露图表实例给父组件
defineExpose({
  chartInstance,
  updateChart,
  getImg,
})
</script>

<style scoped></style>
