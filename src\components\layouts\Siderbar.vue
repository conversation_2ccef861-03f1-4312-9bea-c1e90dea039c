<template>
  <n-flex vertical class="h-100vh bg-#3075F6FF">
    <Logo></Logo>
    <n-layout-sider :native-scrollbar="false" width="338px">
      <n-scrollbar :style="{ maxHeight: 'calc(100vh - 190px)' }">
        <n-menu
          class="select-none"
          :value="activeKey"
          :options="menuOptions"
          @update:value="handleMenuSelect"
        />
      </n-scrollbar>
    </n-layout-sider>
  </n-flex>
</template>

<script setup lang="ts">
import { NLayoutSider, NMenu, NFlex, NScrollbar, type MenuOption } from 'naive-ui'
import Logo from '@/components/layouts/Logo.vue'
import { h, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import icons from '@/utils/constant/icons'
import { renderIcon } from '@/utils/tools'

// 路由实例
const router = useRouter()

/**
 * 一级标题
 * 二级标题
 * 三级标题
 */
const textClassOptions = {
  firstLevel: 'text-xl font-bold',
  secondLevel: 'text-xl font-400',
  thirdLevel: 'text-base font-normal',
}

const activeKey = ref('section-monitoring')

/**
 * 文本 class名称
 */
function renderText(text: string, textLevel: keyof typeof textClassOptions) {
  return () =>
    h(
      'span',
      { class: textClassOptions[textLevel] || textClassOptions.firstLevel },
      { default: () => text },
    )
}

const menuOptions = [
  {
    label: renderText('断面监视', 'firstLevel'),
    key: 'section-monitoring',
    icon: renderIcon(icons.sectionMonitoringIcon),
  },
  {
    label: renderText('执行情况', 'firstLevel'),
    key: 'execution-situation',
    icon: renderIcon(icons.executionSituationIcon),
    children: [
      // {
      //   label: renderText('参与市场', 'secondLevel'),
      //   key: 'participate-market',
      // },
      // {
      //   label: renderText('煤炭', 'secondLevel'),
      //   key: 'coal',
      //   children: [
      //     {
      //       label: renderText('全省', 'thirdLevel'),
      //       key: 'all-province',
      //     },
      //     {
      //       label: renderText('江南', 'thirdLevel'),
      //       key: 'jiangnan',
      //     },
      //     {
      //       label: renderText('江北', 'thirdLevel'),
      //       key: 'jiangbei',
      //     },
      //   ],
      // },
      // {
      //   label: renderText('核电', 'secondLevel'),
      //   key: 'nuclear-power',
      // },
      // {
      //   label: renderText('风电', 'secondLevel'),
      //   key: 'wind-power',
      // },
      // {
      //   label: renderText('光伏', 'secondLevel'),
      //   key: 'photovoltaic',
      // },
      // {
      //   label: renderText('燃煤', 'secondLevel'),
      //   key: 'coal-fired',
      // },
      // {
      //   label: renderText('燃气', 'secondLevel'),
      //   key: 'gas',
      // },
      // {
      //   label: renderText('抽蓄', 'secondLevel'),
      //   key: 'pumped-storage',
      // },
      // {
      //   label: renderText('独立蓄能', 'secondLevel'),
      //   key: 'independent-storage',
      // },
      // {
      //   label: renderText('不参与市场', 'secondLevel'),
      //   key: 'not-participate-market',
      // },
      // {
      //   label: renderText('其他', 'secondLevel'),
      //   key: 'other',
      // },
      {
        label: renderText('新能源市场调峰日报', 'secondLevel'),
        key: 'daily-report',
      },
    ],
  },
  {
    label: renderText('负荷预测', 'firstLevel'),
    key: 'load-forecasting',
    icon: renderIcon(icons.loadForecastingIcon),
    children: [
      // {
      //   label: renderText('统调负荷', 'secondLevel'),
      //   key: 'unified-scheduling-load',
      // },
      {
        label: renderText('母线负荷', 'secondLevel'),
        key: 'busbar-load',
        children: [
          {
            label: renderText('全省', 'thirdLevel'),
            key: 'province',
            route: '/load-forecasting/busbar-load',
          },
          // {
          //   label: renderText('南京', 'thirdLevel'),
          //   key: 'nanjing',
          //   route: '/load-forecasting/busbar-load',
          // },
        ],
      },
    ],
  },
  {
    label: renderText('CPS及频率', 'firstLevel'),
    key: 'cps-frequency',
    icon: renderIcon(icons.cpsFrequencyIcon),
  },
]

//watch 路由变化，更新菜单选中状态
watch(
  () => router.currentRoute.value,
  (newPath) => {
    activeKey.value = newPath.query.key ? (newPath.query.key as string) : (newPath.name as string)
  },
  { immediate: true },
)

/**
 * 如果item.route有值，则将key作为route的参数 ?key=
 * @param key
 * @param item
 */
const handleMenuSelect = (key: string, item: MenuOption) => {
  activeKey.value = key
  if (item.route) {
    router.push({
      path: item.route as string,
      query: { key },
    })
  } else {
    router.push(`/${key}`)
  }
}
</script>
