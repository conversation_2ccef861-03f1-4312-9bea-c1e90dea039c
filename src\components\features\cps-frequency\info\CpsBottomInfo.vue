<template>
  <n-scrollbar class="h-full mb-2">
    <div class="flex pl-5 mb-5 h-70" v-for="item in cpsFrequencyStore.cpsData?.CPSStatisticList">
      <!-- 日期详情数据 -->
      <div class="flex flex-shrink-0 flex-col w-40.5 bg-#FEF4E8 rounded">
        <div class="text-center text-#C76920 text-xl mt-4">
          <n-icon size="16">
            <CalendarAltRegularIcon />
          </n-icon>
          {{ item.date }}
        </div>
        <div class="flex flex-col pl-6">
          <div class="flex items-center -mt-2">
            <div class="flex items-center text-#CC8F60">
              <span class="mb-1 text-5xl mr-2">·</span>
              <span class="text-base">CPS1</span>
            </div>
            <div class="text-#643F1A text-xl ml-2">{{ item.cps1 }}</div>
          </div>
          <div class="flex items-center -mt-5">
            <div class="flex items-center text-#CC8F60">
              <span class="mb-1 text-5xl mr-2">·</span>
              <span class="text-base">CPS2</span>
            </div>
            <div class="text-#643F1A text-xl ml-2">{{ item.cps2 }}</div>
          </div>
          <div class="-mt-5">
            <div class="flex items-center text-#CC8F60">
              <span class="text-5xl mr-2">·</span>
              <span class="mt-1 text-base">全天费用</span>
            </div>
            <div class="text-#643F1A text-xl ml-5 -mt-2">{{ item.allDayFee }}</div>
          </div>
          <div class="-mt-4">
            <div class="flex items-center text-#CC8F60">
              <span class="text-5xl mr-2">·</span>
              <span class="mt-1 text-base">最大频率</span>
            </div>
            <div class="text-#643F1A text-xl ml-5 -mt-2">{{ item.maxRate }}</div>
          </div>
          <div class="-mt-4">
            <div class="flex items-center text-#CC8F60">
              <span class="text-5xl mr-2">·</span>
              <span class="mt-1 text-base">最小频率</span>
            </div>
            <div class="text-#643F1A text-xl ml-5 -mt-2">{{ item.minRate }}</div>
          </div>
        </div>
      </div>
      <!-- 横向表格数据 -->
      <VerticalTable
        class="flex-1 px-5"
        :columns="tableColumns"
        :data="formatTableData(item)"
        width="calc(100vw - 730px)"
      ></VerticalTable>
    </div>
  </n-scrollbar>
</template>

<script setup lang="ts">
import { NIcon, NScrollbar } from 'naive-ui'
import VerticalTable from '@/components/shared/tables/VerticalTable.vue'
import { CalendarAltRegularIcon } from '@/utils/constant/icons'
import { useCpsFrequency } from '@/stores'
import type { CPSStatisticItem } from '@/utils/api'
import { formatTime } from '@/utils/tools'

const cpsFrequencyStore = useCpsFrequency()

// 默认表格列配置
const tableColumns = [
  {
    key: 'time',
    title: '时间',
    height: '10%',
  },
  {
    key: 'cps1',
    title: 'CPS1（全天）',
    height: '10%',
  },
  {
    key: 'cps2',
    title: 'CPS2（全天）',
  },
]

const formatTableData = (item: CPSStatisticItem) => {
  const timeList = item.cps1List.map((item) => formatTime(item.time, 'HH:mm:ss'))
  const CPS1List = item.cps1List.map((item) => item.value)
  const CPS2List = item.cps2List.map((item) => item.value)

  const data = timeList.map((time, index) => ({
    time,
    cps1: CPS1List[index] || '-',
    cps2: CPS2List[index] || '-',
  }))

  return data
}
</script>

<style scoped></style>
