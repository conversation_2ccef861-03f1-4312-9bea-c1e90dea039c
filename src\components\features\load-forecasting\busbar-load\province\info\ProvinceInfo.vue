<template>
  <div class="flex flex-col h-100vh">
    <div class="flex px-9 py-4 bg-#F8F8F8 border-b-1 border-b-#D7D7D7 border-b-solid">
      <div class="flex flex-1 text-2xl text-#9E9E9E">{{ busbarLoadStore.selectedRow?.time }}</div>
      <div class="flex">
        <ExportButton
          class="mr-2.5"
          :data="tableData"
          :columns="tableColumns"
          filename="全省母线负荷数据详情"
        />
        <n-button type="info" size="large" @click="handleBack">
          <template #icon>
            <n-icon><ChevronLeft20FilledIcon /></n-icon>
          </template>
          返回
        </n-button>
      </div>
    </div>
    <!-- 柱状图 -->
    <BarChart title="13地市日均偏差柱状图" :data="chartData" height="370px" class="mt-2" />

    <div class="flex flex-col px-5">
      <hr class="w-full shrink-0 border-t border-[#C0C0C0] border-dashed my-2.5" />
      <n-select
        v-model:value="selectedSortOption"
        class="w-42.5 mb-2 self-end"
        :options="sortOptions"
      ></n-select>
      <!-- 横向表格数据 -->
      <VerticalTable class="flex-1" :columns="tableColumns" :data="tableData"></VerticalTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeUnmount, onMounted, ref } from 'vue'
import { NButton, NIcon, NSelect } from 'naive-ui'
import { ChevronLeft20FilledIcon } from '@/utils/constant/icons'
import ExportButton from '@/components/shared/buttons/ExportButton.vue'
import BarChart from '@/components/shared/charts/BarChart.vue'
import VerticalTable from '@/components/shared/tables/VerticalTable.vue'

import { useBusbarLoad } from '@/stores'
import { parseTime } from '@/utils/tools'
import { BusbarLoadService, type BusbarLoadDataDetailItem } from '@/utils/api/services/busbarLoad'

const busbarLoadStore = useBusbarLoad()
const detailData = ref<BusbarLoadDataDetailItem[]>([])

// 柱状图数据
const chartData = computed(() => {
  if (!detailData.value) return []

  return [
    {
      name: '负荷预测',
      color: '#3075F6',
      data: detailData.value.map((item) => ({
        name: item.area,
        value: parseFloat(item.usPrediction),
      })),
    },
    {
      name: '实时负荷',
      color: '#FFB637',
      data: detailData.value.map((item) => ({
        name: item.area,
        value: parseFloat(item.rtValue),
      })),
    },
  ]
})

const tableColumns = [
  {
    title: '地市名称',
    key: 'area',
  },
  { title: '短期预测', key: 'sPrediction' },
  { title: '超短期预测', key: 'usPrediction' },
  { title: '实际负荷', key: 'rtValue' },
  { title: '负荷偏差', key: 'usDiff' },
  { title: '偏差率', key: 'usDiffRate' },
  { title: '日均偏差', key: 'dailyAvgUsDiff' },
  { title: '日均偏差率', key: 'dailyAvgUsDiffRate' },
  { title: 'MAE', key: 'mae' },
  { title: 'RMSE', key: 'rmse' },
  { title: 'MAPE', key: 'mape' },
]

const selectedSortOption = ref<keyof BusbarLoadDataDetailItem>('dailyAvgUsDiff')
const sortOptions = [
  {
    label: '按日均偏差',
    value: 'dailyAvgUsDiff',
  },
  {
    label: '按日均偏差率',
    value: 'dailyAvgUsDiffRate',
  },
  {
    label: '按MAPE',
    value: 'mape',
  },
  {
    label: '按RMSE',
    value: 'rmse',
  },
  {
    label: '按MAE',
    value: 'mae',
  },
]

const tableData = computed<BusbarLoadDataDetailItem[]>(() => {
  if (!detailData.value) return []

  return detailData.value
    .map((item) => ({ ...item }))
    .sort((a, b) => {
      return parseFloat(b[selectedSortOption.value]) - parseFloat(a[selectedSortOption.value])
    })
})

const handleBack = () => {
  busbarLoadStore.selectedRow = null
  // 跳转到ProvinceStatistic页面
  busbarLoadStore.isInfoVisible = false
  busbarLoadStore.isDetailVisible = false
}

//getBusbarLoadDataByTime
const fetchBusbarLoadDataByTime = async () => {
  if (!busbarLoadStore.selectedRow) return
  try {
    const params = {
      time: parseTime(busbarLoadStore.selectedRow.time, true),
    }
    const response = await BusbarLoadService.getBusbarLoadDataByTime(params)
    detailData.value = response
    console.log('response:', response)
  } catch (error) {
    console.error('获取全省母线负荷单个时间详情失败:', error)
  }
}

onMounted(() => {
  fetchBusbarLoadDataByTime()
})

onBeforeUnmount(() => {
  busbarLoadStore.selectedRow = null
  busbarLoadStore.isInfoVisible = false
  busbarLoadStore.isDetailVisible = false
})
</script>

<style scoped></style>
