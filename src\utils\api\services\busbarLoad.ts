import { API_ENDPOINTS } from '../config'
import { api } from '../instance'

// 查获取全省母线负荷统计信息明细
export interface BusbarLoadDetailQueryParams {
  startDay?: string
  endDay?: string
}

/**
 * 母线负荷数据接口
 * time	时间
 * usPrediction	超短期预测值
 * rtValue	实测值
 * usDiff	差值
 */
export interface BusbarLoadDataItem {
  time: string
  usPrediction: string
  rtValue: string
  usDiff: string
}

/**
 * 母线负荷详情数据接口
 * area 地市名称
 * sPrediction	短期预测值
 * usPrediction	超短期预测值
 * rtValue	实时负荷
 * usDiff	超短期负荷偏差
 * usDiffRate	超短期负荷偏差率
 * dailyAvgUsDiff	超短期日均负荷偏差
 * dailyAvgUsDiffRate	超短期日均负荷偏差率
 * mae	MAE
 * mape	MAPE
 * rmse	RMSE
 */
export interface BusbarLoadDataDetailItem {
  area: string
  sPrediction: string
  usPrediction: string
  rtValue: string
  usDiff: string
  usDiffRate: string
  dailyAvgUsDiff: string
  dailyAvgUsDiffRate: string
  mae: string
  mape: string
  rmse: string
}

/**
 * 母线负荷详情数据接口
 * time	时间
 * usPrediction	超短期预测值
 * sPrediction	短期预测值
 * rtValue	实测值
 * usDiff	超短期偏差
 * usDiffRate	超短期偏差率
 */
export interface BusLfDataItem {
  time: string
  usPrediction: string
  sPrediction: string
  rtValue: string
  usDiff: string
  usDiffRate: string
}

// 母线负荷统计数据接口
export interface BusLfStatisticItem {
  date: string
  name: string
  dailyAvgUsDiff: string
  dailyAvgUsDiffRate: string
  mae: string
  rmse: string
  mape: string
  busLfDataList: BusLfDataItem[]
}

/**
 * 母线负荷详情响应数据接口
 * date	日期
 * name	名称
 * dailyAvgUsDiff	日平均偏差
 * dailyAvgUsDiffRate	日平均偏差率
 * busLfDataList	详细列表
 * mae	MAE
 * rmse	RMSE
 * mape	MAPE
 */
export interface BusbarLoadDataDetailResponse {
  busLfDataList: BusLfDataItem[]
  busLfStatisticList: BusLfStatisticItem[]
}

export class BusbarLoadService {
  /**
   * 获取全省母线负荷数据
   * @param params 查询参数
   * @returns 母线负荷数据
   */
  static async getBusbarLoadData(params: { day: string }) {
    return api.get<BusbarLoadDataItem[]>(API_ENDPOINTS.LOAD_FORECASTING.BUSBAR_LOAD_DATA, params)
  }

  /**
   * 获取全省母线负荷单个时间详情
   * @param params 查询参数
   * @returns 母线负荷单个时间详情
   */
  static async getBusbarLoadDataByTime(params: { time: string }) {
    return api.get<BusbarLoadDataDetailItem[]>(
      API_ENDPOINTS.LOAD_FORECASTING.BUSBAR_LOAD_DATA_BY_TIME,
      params,
    )
  }
  /**
   * 获取全省母线负荷统计信息明细
   * @param params 查询参数
   * @returns 母线负荷统计信息明细
   */
  static async getBusbarLoadDataDetail(params: BusbarLoadDetailQueryParams) {
    return api.get<BusbarLoadDataDetailResponse>(
      API_ENDPOINTS.LOAD_FORECASTING.BUSBAR_LOAD_DATA_DETAIL,
      params,
    )
  }
}
