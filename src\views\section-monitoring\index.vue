<template>
  <KeepAlive :include="['SectionStatistic']">
    <component
      :is="!!sectionMonitoringStore.selectedTableRow ? SectionInfo : SectionStatistic"
    ></component>
  </KeepAlive>
</template>

<script setup lang="ts">
import { useSectionMonitoringStore } from '@/stores/'

import SectionStatistic from '@/components/features/section-monitoring/overview/SectionStatistic.vue'
import SectionInfo from '@/components/features/section-monitoring/info/SectionInfo.vue'

const sectionMonitoringStore = useSectionMonitoringStore()
</script>
