<template>
  <div class="flex px-7.5 py-4 items-center bg-#F8F8F8 border-b-1 border-b-#D7D7D7 border-b-solid">
    <span class="text-2xl text-#9E9E9E mr-9">查询结果</span>

    <div class="flex flex-1">
      <InputGroup size="large" label="时间" class="mr-2.5 max-w-500px">
        <n-date-picker
          v-model:value="timeRange"
          class="w-full"
          type="datetimerange"
          size="large"
          placeholder="请选择时间"
          :is-date-disabled="disablePreviousDate"
        >
        </n-date-picker>
      </InputGroup>
    </div>
    <div class="flex">
      <n-button type="info" class="mr-2.5" size="large" @click="handleSearch" :loading="loading">
        <template #icon>
          <n-icon><SearchIcon /></n-icon>
        </template>
        搜索
      </n-button>
      <ExportButton
        :data="tableData"
        :columns="columns"
        filename="断面单日统计列表"
        button-class="mr-2.5"
      />

      <n-button type="info" size="large" @click="handleBack">
        <template #icon>
          <n-icon><ChevronLeft20FilledIcon /></n-icon>
        </template>
        返回
      </n-button>
    </div>
  </div>
  <LineChart title="断面潮流曲线" :data="multiSeriesData" height="450px" class="mt-2" />

  <SectionDetail :section-data="sectionDetailData"></SectionDetail>
  <DataTable
    class="p-5"
    :columns="columns"
    :data="tableData"
    :loading="loading"
    height="calc(100vh - 740px)"
    @sort="handleSort"
  >
  </DataTable>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, toRaw, onBeforeUnmount } from 'vue'
import { NDatePicker, NButton, NIcon } from 'naive-ui'

import InputGroup from '@/components/shared/InputGroup.vue'
import { SearchIcon, ChevronLeft20FilledIcon } from '@/utils/constant/icons'
import LineChart from '@/components/shared/charts/LineChart.vue'
import DataTable from '@/components/shared/tables/DataTable.vue'
import ExportButton from '@/components/shared/buttons/ExportButton.vue'
import SectionDetail from './SectionDetail.vue'
import { useSectionMonitoringStore } from '@/stores'
import {
  SectionMonitoringService,
  type SectionStatisticDetailResponse,
  type SectionStatisticDetailParams,
  type SectionStatisticDetailStatisticDataPoint,
  type SectionStatisticDetailDataPoint,
} from '@/utils/api'
import type { SeriesData } from '@/types/chart'
import {
  formatTime,
  getTodayTimeRange,
  isTimeRangeCrossDay,
  disablePreviousDate,
} from '@/utils/tools/'
import { applyDefaultSort, applySorting, compareNumericDesc } from '@/utils/sort/'

const timestampRange = getTodayTimeRange()

let sectionDetailRawData: SectionStatisticDetailResponse | null = null

// 响应式数据
const sectionMonitoringStore = useSectionMonitoringStore()
const loading = ref(false)
const sectionDetailData = ref<SectionStatisticDetailResponse | null>(null)
const multiSeriesData = ref<SeriesData[]>([])

// 时间选择器数据
const timeRange = ref<[number, number]>([timestampRange[0], timestampRange[1]])

// 图表数据
const formatMultiSeriesData = () => {
  if (!sectionDetailRawData) {
    return []
  }

  multiSeriesData.value = [
    {
      name: '潮流值',
      color: '#004EE4',
      gradientColors: {
        start: 'rgba(0, 117, 207, 1)',
        end: 'rgba(255, 255, 255, 1)',
      },
      data: sectionDetailRawData.dataList.map((item) => ({
        name: item.time,
        value: item.value,
      })),
    },
    {
      name: '限额值',
      color: 'rgba(230, 176, 46, 1)',
      data: sectionDetailRawData.dataList.map((item) => ({
        name: item.time,
        value: item.limit,
      })),
    },
  ]
}

// 跨天时的列配置（完整列）
const crossDayColumns = [
  {
    key: 'time',
    title: '日期',
    align: 'center' as const,
    width: '12%',
  },
  {
    key: 'overPeriod',
    title: '越限时间段',
    align: 'center' as const,
    width: '15%',
  },
  {
    key: 'limit',
    title: '限额',
    align: 'center' as const,
    width: '12%',
  },
  {
    key: 'maxValue',
    title: '最大潮流',
    align: 'center' as const,
    width: '12%',
    sorter: {
      compare: compareNumericDesc('maxValue'),
      multiple: 2,
    },
    defaultSortOrder: false as const,
  },
  {
    key: 'maxDiffValue',
    title: '差额最大值',
    align: 'center' as const,
    width: '12%',
    sorter: {
      compare: compareNumericDesc('maxDiffValue'),
      multiple: 3,
    },
    defaultSortOrder: false as const,
  },
  {
    key: 'totalOverTime',
    title: '总越限时间',
    align: 'center' as const,
    width: '12%',
    sorter: {
      compare: compareNumericDesc('totalOverTime'),
      multiple: 4,
    },
    defaultSortOrder: false as const,
  },
  {
    key: 'longestOverTime',
    title: '最长出现越限时长',
    align: 'center' as const,
    width: '15%',
    sorter: {
      compare: compareNumericDesc('longestOverTime'),
      multiple: 5,
    },
  },
]

// 未跨天时的列配置（简化列）
const sameDayColumns = [
  {
    key: 'time',
    title: '时间',
    align: 'center' as const,
    defaultSortOrder: false as const,
  },
  {
    key: 'limit',
    title: '限额',
    align: 'center' as const,
  },
  {
    key: 'value',
    title: '实时潮流',
    align: 'center' as const,
    sorter: {
      compare: compareNumericDesc('value'),
      multiple: 1,
    },
    defaultSortOrder: false as const,
  },
  {
    key: 'diffValue',
    title: '差额',
    align: 'center' as const,
    sorter: {
      compare: compareNumericDesc('diffValue'),
      multiple: 1,
    },
    defaultSortOrder: false as const,
  },
]

// 动态列配置
const columns = computed(() => {
  const isCrossDay = isTimeRangeCrossDay(timeRange.value)
  return isCrossDay ? crossDayColumns : sameDayColumns
})

// 表格数据
const tableData = computed<
  SectionStatisticDetailDataPoint[] | SectionStatisticDetailStatisticDataPoint[]
>(() => {
  const isCrossDay = isTimeRangeCrossDay(timeRange.value)

  if (isCrossDay) {
    return sectionDetailData.value?.statisticDataList || []
  } else {
    return sectionDetailData.value?.dataList || []
  }
})

// 获取断面统计详情数据
const fetchSectionStatisticDetail = async () => {
  if (!sectionMonitoringStore.selectedTableRow || !timeRange.value) {
    return
  }

  loading.value = true
  try {
    const params: SectionStatisticDetailParams = {
      startTime: formatTime(timeRange.value[0]),
      endTime: formatTime(timeRange.value[1]),
      sectionId: sectionMonitoringStore.selectedTableRow.id,
    }

    const response = await SectionMonitoringService.getSectionStatisticDetail(params)
    const statisticDataList = response.statisticDataList
      ? applyDefaultSort(response.statisticDataList)
      : []

    sectionDetailData.value = {
      ...response,
      statisticDataList,
    }
    sectionDetailRawData = {
      ...response,
      statisticDataList,
    }

    formatMultiSeriesData()
  } catch (error) {
    console.error('获取断面统计详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索处理函数
const handleSearch = () => {
  fetchSectionStatisticDetail()
}

// 排序处理函数
const handleSort = (column: any, order: 'asc' | 'desc' | null, multiSortState: any) => {
  console.log('详情页排序:', column.key, order, multiSortState)
  const isCrossDay = isTimeRangeCrossDay(timeRange.value)

  if (!order && Object.keys(multiSortState).length === 0) {
    // 防止排序时，曲线图的数据也跟着排序
    // 恢复默认排序 - 重新获取数据
    sectionDetailData.value = JSON.parse(JSON.stringify(sectionDetailRawData))
    return
  }

  if (!sectionDetailData.value) return
  // 使用公共的多列排序函数
  // 注意：这里需要直接操作原始数据，因为computed属性会自动重新计算
  const { dataList, statisticDataList = [] } = sectionDetailData.value
  const rawData = isCrossDay ? statisticDataList : dataList
  const adaptedData = rawData.map((item, index) => ({
    ...item,
    id: String(index + 1),
    name: `数据项${index + 1}`,
    volt: '220KV',
  }))

  const sortedData = applySorting(adaptedData, multiSortState, columns.value)

  if (isCrossDay) {
    // 跨天时，需要将排序后的数据映射回原始数据结构
    sectionDetailData.value.statisticDataList = sortedData.map((item) => {
      const { id, name, volt, ...originalItem } = item
      return originalItem
    }) as SectionStatisticDetailStatisticDataPoint[]
  } else {
    // 未跨天时，直接更新dataList
    sectionDetailData.value.dataList = sortedData as SectionStatisticDetailDataPoint[]
  }
}

const handleBack = () => {
  sectionMonitoringStore.selectedTableRow = null
}

// 监听选中行变化，自动获取数据
// watch(
//   () => sectionMonitoringStore.selectedTableRow,
//   (newRow) => {
//     if (newRow && timeRange.value) {
//       fetchSectionStatisticDetail()
//     }
//   },
//   { immediate: true },
// )

// 监听时间范围变化，动态切换列配置
// watch(
//   () => timeRange.value,
//   (newTimeRange) => {
//     console.log('时间范围变化，当前是否跨天:', isTimeRangeCrossDay(newTimeRange))
//     // 时间范围变化时，列配置会自动更新（通过computed属性）
//     // 如果需要重新获取数据，可以在这里调用
//     if (sectionMonitoringStore.selectedTableRow && newTimeRange) {
//       fetchSectionStatisticDetail()
//     }
//   },
//   { deep: true },
// )

// 组件挂载时设置默认时间范围
onMounted(() => {
  // 优先使用概览页面的时间范围，如果没有则使用当天时间范围
  if (sectionMonitoringStore.overviewTimeRange) {
    timeRange.value = sectionMonitoringStore.overviewTimeRange
  } else {
    // 使用当天时间范围作为默认值
    timeRange.value = [timestampRange[0], timestampRange[1]]
  }

  // 如果有选中的行，则获取数据
  if (sectionMonitoringStore.selectedTableRow) {
    fetchSectionStatisticDetail()
  }
})

onBeforeUnmount(() => {
  sectionMonitoringStore.selectedTableRow = null
})
</script>
